<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- File appender -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/langgraph4j-demo.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Logger for LangGraph4j -->
    <logger name="org.bsc.langgraph4j" level="INFO" />
    
    <!-- Logger for LangChain4j -->
    <logger name="dev.langchain4j" level="INFO" />
    
    <!-- Logger for our demo -->
    <logger name="com.example.langgraph4j.demo" level="DEBUG" />

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
